# Input plugin to collect Windows Event Log messages
# This plugin ONLY supports Windows
[[inputs.win_eventlog]]
  ## Telegraf should have Administrator permissions to subscribe for some
  ## Windows Events channels (e.g. System log)

  ## LCID (Locale ID) for event rendering
  ## 1033 to force English language
  ## 0 to use default Windows locale
  # locale = 0

  ## Name of eventlog, used only if xpath_query is empty
  ## Example: "Application"
  # eventlog_name = ""

  ## xpath_query can be in defined short form like "Event/System[EventID=999]"
  ## or you can form a XML Query. Refer to the Consuming Events article:
  ## https://docs.microsoft.com/en-us/windows/win32/wes/consuming-events
  ## XML query is the recommended form, because it is most flexible
  ## You can create or debug XML Query by creating Custom View in Windows Event Viewer
  ## and then copying resulting XML here
  xpath_query = '''
  <QueryList>
    <Query Id="0" Path="Security">
      <Select Path="Security">*[System[(Level &lt; 4)]]</Select>
    </Query>
    <Query Id="1" Path="Application">
      <Select Path="Application">*[System[(Level &lt; 4)]]</Select>
    </Query>
    <Query Id="2" Path="System">
      <Select Path="System">*[System[(Level &lt; 4)]]</Select>
    </Query>
  </QueryList>
  '''

  ## When true, event logs are read from the beginning; otherwise only future
  ## events will be logged.
  # from_beginning = false

  # Process UserData XML to fields, if this node exists in Event XML
  # process_userdata = true

  # Process EventData XML to fields, if this node exists in Event XML
  # process_eventdata = true

  ## Separator character to use for unrolled XML Data field names
  # separator = "_"

  ## Get only first line of Message field. For most events first line is
  ## usually more than enough
  # only_first_line_of_message = true

  ## Parse timestamp from TimeCreated.SystemTime event field.
  ## Will default to current time of telegraf processing on parsing error or if
  ## set to false
  # timestamp_from_event = true

  ## System field names:
  ##   "Source", "EventID", "Version", "Level", "Task", "Opcode", "Keywords",
  ##   "TimeCreated", "EventRecordID", "ActivityID", "RelatedActivityID",
  ##   "ProcessID", "ThreadID", "ProcessName", "Channel", "Computer", "UserID",
  ##   "UserName", "Message", "LevelText", "TaskText", "OpcodeText"
  ##
  ## In addition to System, Data fields can be unrolled from additional XML
  ## nodes in event. Human-readable representation of those nodes is formatted
  ## into event Message field, but XML is more machine-parsable

  ## Event fields to include as tags
  ## The values below are included by default.
  ## Globbing supported (e.g. "Level*" matches both "Level" and "LevelText")
  event_tags = ["Source", "EventID", "Level", "Channel", "Computer"]

  ## Event fields to include
  ## All fields are sent by default.
  ## Globbing supported (e.g. "Level*" matches both "Level" and "LevelText")
  event_fields = ["EventID", "Level", "Source", "TimeCreated", "Computer", "Channel"]

  ## Event fields to exclude
  ## Note that if you exclude all fields then no metrics are produced. A valid
  ## metric includes at least one field.
  ## Globbing supported (e.g. "Level*" matches both "Level" and "LevelText")
  exclude_fields = ["Message", "UserName", "ProcessName", "ActivityID", "RelatedActivityID", "ThreadID", "ProcessID", "UserID", "Keywords", "TaskText", "OpcodeText"]

  ## Event fields to exclude if their value is empty or equals to zero
  ## The values below are included by default.
  ## Globbing supported (e.g. "Level*" matches both "Level" and "LevelText")
  # exclude_empty = ["Task", "Opcode", "*ActivityID", "UserID"]

  ## Reduce processing overhead
  process_userdata = false
  process_eventdata = false
  only_first_line_of_message = true
